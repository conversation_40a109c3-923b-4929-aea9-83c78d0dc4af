<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="apple-touch-icon" sizes="180x180" href="/favicon.svg">
    <link rel="icon" type="image/svg+xml" sizes="32x32" href="/favicon.svg">
    <link rel="icon" type="image/svg+xml" sizes="16x16" href="/favicon.svg">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>YNNX AI 开发者平台 - 云南农信智能开发助手</title>
    
    
    <!-- 内网部署：移除外部CDN预连接，所有资源已本地化 -->
    <!-- 原外部预连接已禁用，确保内网环境正常运行 -->
    
    <!-- Font Awesome本地资源 - 立即加载确保图标显示 -->
    <link rel="stylesheet" href="/assets/fonts/fontawesome.css">
    <link rel="preload" href="/assets/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/assets/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
    
    <!-- 优化字体预加载 -->
    <link rel="preload" href="/assets/fonts/optimized-fonts.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="/assets/fonts/optimized-fonts.css"></noscript>
    
    <!-- Meta tags for SEO and performance -->
    <meta name="description" content="YNNX AI 开发者平台 - 云南农信智能AI开发助手，提供代码生成、智能问答、API管理等功能，支持VS Code插件下载">
    <meta name="keywords" content="AI开发助手,代码生成,智能编程,VS Code插件,云南农信,YNNX,API管理">
    <meta name="author" content="云南农信科技结算中心">
    
    <!-- Open Graph tags -->
    <meta property="og:title" content="YNNX AI 开发者平台">
    <meta property="og:description" content="云南农信智能AI开发助手，提升开发效率">
    <meta property="og:type" content="website">
    
    <!-- Performance hints -->
    <meta name="theme-color" content="#000000">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    
    <!-- Critical CSS inlined would go here for further optimization -->
    <style>
      /* Critical CSS for initial paint */
      body {
        margin: 0;
        background-color: #000;
        color: #fff;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      }
      
      .loading-fallback {
        position: fixed;
        inset: 0;
        background: #000;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-text {
        color: #06b6d4;
        font-size: 1.2rem;
        -webkit-animation: pulse 2s infinite;
        animation: pulse 2s infinite;
      }
      
      @keyframes pulse {
        0%, 100% { opacity: 0.5; }
        50% { opacity: 1; }
      }
      
      @-webkit-keyframes pulse {
        0%, 100% { opacity: 0.5; }
        50% { opacity: 1; }
      }
    </style>
    
    <!-- 浏览器兼容性早期检测 -->
    <script>
      // 浏览器兼容性检测
      if (typeof window !== 'undefined') {
        // 检测关键API支持
        const isSupported = !!(
          window.fetch &&
          window.Promise &&
          window.Map &&
          window.Set &&
          window.Symbol &&
          Object.assign &&
          Array.from &&
          window.requestAnimationFrame &&
          window.addEventListener
        );

        if (!isSupported) {
          document.body.innerHTML = `
            <div style="padding: 20px; font-family: Arial, sans-serif; text-align: center;">
              <h2 style="color: #d32f2f;">浏览器不兼容 ⚠️</h2>
              <p>您的浏览器版本过低，无法正常使用本应用。</p>
              <p>请升级到以下浏览器的最新版本：</p>
              <ul style="text-align: left; display: inline-block;">
                <li>Chrome 88+</li>
                <li>Firefox 78+</li>
                <li>Safari 14+</li>
                <li>Edge 88+</li>
              </ul>
            </div>
          `;
          throw new Error('浏览器不兼容');
        }

        // 注册 Service Worker（仅在生产环境）
        if ('serviceWorker' in navigator && window.location.hostname !== 'localhost') {
          navigator.serviceWorker.register('/sw.js')
            .then(registration => {
              console.log('SW 注册成功:', registration);
            })
            .catch(error => {
              console.error('SW 注册失败:', error);
            });
        } else {
          console.log('开发环境：跳过 Service Worker 注册');
        }
      }
    </script>
  </head>
  <body>
    <div id="root">
      <!-- Fallback loading UI -->
      <div class="loading-fallback">
        <div class="loading-text">YNNX AI Platform Loading...</div>
      </div>
    </div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html> 