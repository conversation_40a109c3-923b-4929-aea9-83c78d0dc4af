# 多阶段构建：构建阶段
FROM node:22-alpine AS builder

# 设置工作目录
WORKDIR /app

# 配置npm使用国内镜像
RUN npm config set registry https://registry.npmmirror.com/

# 复制依赖文件
COPY package*.json ./

# 安装全部依赖（构建需要）
RUN npm ci --silent

# 设置构建时环境变量
ARG VITE_LDAP_API_URL=http://192.168.1.3:3002
ARG VITE_LITELLM_API_BASE=http://192.168.1.3:4000
ENV VITE_LDAP_API_URL=$VITE_LDAP_API_URL
ENV VITE_LITELLM_API_BASE=$VITE_LITELLM_API_BASE

# 复制源代码
COPY . .

# 构建前端
RUN npm run build

# 生产阶段：运行时镜像
FROM node:22-alpine AS runtime

# 设置工作目录
WORKDIR /app

# 配置npm使用国内镜像
RUN npm config set registry https://registry.npmmirror.com/

# 只复制生产依赖
COPY package*.json ./
RUN npm ci --silent --only=production

# 复制构建产物
COPY --from=builder /app/dist ./dist

# 复制源代码（服务端需要）
COPY src ./src
COPY ecosystem.config.js ./

# 暴露端口
EXPOSE 3002

# 创建启动脚本
RUN echo '#!/bin/sh' > /app/start.sh && \
    echo 'echo "Starting LDAP Auth Server..."' >> /app/start.sh && \
    echo 'node src/server/ldapAuthServer.js &' >> /app/start.sh && \
    echo 'echo "LDAP Auth Server started on port 3002"' >> /app/start.sh && \
    echo 'wait' >> /app/start.sh && \
    chmod +x /app/start.sh

# 健康检查
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3002/health', (res) => process.exit(res.statusCode === 200 ? 0 : 1)).on('error', () => process.exit(1))"

# 启动应用
CMD ["/app/start.sh"] 