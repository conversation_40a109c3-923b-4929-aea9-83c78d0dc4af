// API密钥管理服务 - 与LiteLLM代理的Virtual Keys API交互
import { getLiteLLMApiBase } from '../config/apiConfig.js';

const LITELLM_API_BASE = getLiteLLMApiBase();

class APIKeyService {
  constructor() {
    // 管理员主密钥，从配置文件统一配置
    // 优先级：环境变量 > localStorage > 默认值
    this.masterKey = this.getMasterKeyFromConfig();
    
    // 添加缓存机制
    this.cache = {
      userInfo: new Map(),
      userKeys: new Map(),
      connection: null,
      masterKeyValid: null,
      lastConnectionCheck: 0,
      lastMasterKeyCheck: 0
    };
    
    // 缓存有效期（毫秒）
    this.cacheTimeout = {
      userInfo: 2 * 60 * 1000,    // 用户信息缓存2分钟
      userKeys: 1 * 60 * 1000,    // 用户密钥缓存1分钟
      connection: 5 * 60 * 1000,  // 连接状态缓存5分钟
      masterKey: 5 * 60 * 1000    // 主密钥验证缓存5分钟
    };
  }

  // 从配置获取主密钥
  getMasterKeyFromConfig() {
    // 在实际部署时，应该从服务端配置获取
    // 这里先从localStorage获取，如果没有则使用默认值
    const storedKey = localStorage.getItem('litellm_master_key');
    const defaultKey = 'sk-ynnx-llm-20250530'; // 这应该与LiteLLM容器配置中的master_key一致
    
    const masterKey = storedKey || defaultKey;
    
    // 记录主密钥来源，便于调试
    if (storedKey) {
      console.log('[API密钥服务] 使用存储的主密钥');
    } else {
      console.log('[API密钥服务] 使用默认主密钥 - 如果是清除缓存后首次访问，可能需要重新配置');
    }
    
    return masterKey;
  }

  // 检查主密钥状态
  getMasterKeyStatus() {
    const storedKey = localStorage.getItem('litellm_master_key');
    return {
      hasStoredKey: !!storedKey,
      usingDefault: !storedKey,
      currentKey: this.masterKey
    };
  }

  // 设置主密钥（仅限内部使用，前端不再暴露此功能）
  _setMasterKey(key) {
    this.masterKey = key;
    localStorage.setItem('litellm_master_key', key);
    console.log('[API密钥服务] 主密钥已更新');
  }

  // 获取请求头
  getHeaders() {
    // 检查主密钥状态，如果使用默认密钥给出警告
    const keyStatus = this.getMasterKeyStatus();
    if (keyStatus.usingDefault) {
      console.warn('[API密钥服务] 正在使用默认主密钥，如果认证失败，可能需要重新配置主密钥');
    }
    
    return {
      'Authorization': `Bearer ${this.masterKey}`,
      'Content-Type': 'application/json'
    };
  }

  // 创建用户
  async createUser(userEmail, userName = null) {
    try {
      const response = await fetch(`${LITELLM_API_BASE}/user/new`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({
          user_email: userEmail,
          user_alias: userName || userEmail.split('@')[0]
        })
      });

      if (!response.ok) {
        throw new Error(`创建用户失败: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('创建用户错误:', error);
      throw error;
    }
  }

  // 生成API密钥
  async generateKey(options = {}) {
    try {
      const {
        user_id,
        models = ['deepseek-chat', 'qwen3-235b-a22b'],
        max_budget = null,
        duration = null,
        metadata = {},
        aliases = {},
        key_alias = null
      } = options;

      const requestBody = {
        models,
        metadata: {
          ...metadata,
          created_by: 'ynnx-ai-platform'
        }
      };

      if (user_id) {
        requestBody.user_id = user_id;
        // 设置Key Alias为用户ID，便于在LiteLLM中区分用户
        requestBody.key_alias = key_alias || user_id;
      }
      if (max_budget) requestBody.max_budget = max_budget;
      if (duration) requestBody.duration = duration;
      if (Object.keys(aliases).length > 0) requestBody.aliases = aliases;

      const response = await fetch(`${LITELLM_API_BASE}/key/generate`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`生成密钥失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      // 保存完整的Secret Key到localStorage，以便后续获取
      if (result.key && result.user_id) {
        // 使用user_id作为key，因为每个用户只能有一个密钥
        localStorage.setItem(`litellm_key_${result.user_id}`, result.key);
        
        // 清理用户缓存，确保下次获取最新数据
        this.clearUserCache(result.user_id);
      }
      
      return result;
    } catch (error) {
      console.error('生成密钥错误:', error);
      throw error;
    }
  }

  // 获取密钥信息
  async getKeyInfo(key) {
    try {
      const response = await fetch(`${LITELLM_API_BASE}/key/info?key=${key}`, {
        method: 'GET',
        headers: this.getHeaders()
      });

      if (!response.ok) {
        throw new Error(`获取密钥信息失败: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('获取密钥信息错误:', error);
      throw error;
    }
  }

  // 获取用户信息（带缓存）
  async getUserInfo(userId) {
    try {
      // 清理过期缓存
      this.cleanExpiredCache();
      
      // 检查缓存
      const cached = this.getCachedData(this.cache.userInfo, userId, this.cacheTimeout.userInfo);
      if (cached) {
        console.log(`[缓存] 使用缓存的用户信息: ${userId}`);
        return cached;
      }

      console.log(`[API] 获取用户信息: ${userId}`);
      const response = await fetch(`${LITELLM_API_BASE}/user/info?user_id=${userId}`, {
        method: 'GET',
        headers: this.getHeaders()
      });

      if (!response.ok) {
        throw new Error(`获取用户信息失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      // 缓存结果
      this.setCachedData(this.cache.userInfo, userId, data);
      
      return data;
    } catch (error) {
      console.error('获取用户信息错误:', error);
      throw error;
    }
  }

  // 删除密钥
  async deleteKey(key) {
    try {
      console.log(`[API密钥服务] 开始删除密钥: ${key ? key.substring(0, 10) + '...' : '未知'}`);
      
      const response = await fetch(`${LITELLM_API_BASE}/key/delete`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({ keys: [key] })
      });

      if (!response.ok) {
        // 根据不同的HTTP状态码提供更详细的错误信息
        let errorMessage = `删除密钥失败: ${response.status} ${response.statusText}`;
        
        if (response.status === 401) {
          const keyStatus = this.getMasterKeyStatus();
          if (keyStatus.usingDefault) {
            errorMessage = `认证失败 (401): 正在使用默认主密钥，可能与服务器配置不匹配。请联系管理员确认主密钥配置。`;
          } else {
            errorMessage = `认证失败 (401): 主密钥无效或已过期。请联系管理员重新配置主密钥。`;
          }
        } else if (response.status === 403) {
          errorMessage = `权限不足 (403): 当前主密钥没有删除密钥的权限。请联系管理员。`;
        } else if (response.status === 404) {
          errorMessage = `密钥不存在 (404): 要删除的密钥不存在，可能已被删除。`;
        } else if (response.status === 500) {
          errorMessage = `服务器错误 (500): LiteLLM服务内部错误，请稍后重试或联系管理员。`;
        }
        
        throw new Error(errorMessage);
      }

      const result = await response.json();
      console.log('[API密钥服务] 密钥删除成功:', result);
      
      // 清理localStorage中的密钥数据
      // 由于我们使用user_id作为localStorage的key，需要找到对应的用户ID
      // 遍历所有localStorage项，找到对应的密钥并删除
      for (let i = localStorage.length - 1; i >= 0; i--) {
        const storageKey = localStorage.key(i);
        if (storageKey && storageKey.startsWith('litellm_key_')) {
          // 删除所有相关的密钥存储，因为用户只能有一个密钥
          localStorage.removeItem(storageKey);
          
          // 清理对应用户的缓存
          const userId = storageKey.replace('litellm_key_', '');
          this.clearUserCache(userId);
        }
      }

      return result;
    } catch (error) {
      console.error('[API密钥服务] 删除密钥错误:', error);
      
      // 如果是网络错误，提供更友好的错误信息
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new Error('网络连接失败: 无法连接到LiteLLM服务，请检查网络连接和服务状态');
      }
      
      // 其他错误直接抛出
      throw error;
    }
  }

  // 阻止密钥
  async blockKey(key) {
    try {
      const response = await fetch(`${LITELLM_API_BASE}/key/block`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({ key })
      });

      if (!response.ok) {
        throw new Error(`阻止密钥失败: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('阻止密钥错误:', error);
      throw error;
    }
  }

  // 解除阻止密钥
  async unblockKey(key) {
    try {
      const response = await fetch(`${LITELLM_API_BASE}/key/unblock`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({ key })
      });

      if (!response.ok) {
        throw new Error(`解除阻止密钥失败: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('解除阻止密钥错误:', error);
      throw error;
    }
  }

  // 更新密钥信息
  async updateKey(key, updates = {}) {
    try {
      const response = await fetch(`${LITELLM_API_BASE}/key/update`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({
          key,
          ...updates
        })
      });

      if (!response.ok) {
        throw new Error(`更新密钥失败: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('更新密钥错误:', error);
      throw error;
    }
  }

  // 重新生成密钥（由于LiteLLM重新生成功能是企业版功能，这里用删除+创建的方式实现）
  async regenerateKey(key, options = {}) {
    try {
      // 1. 首先获取原密钥信息
      const keyInfo = await this.getKeyInfo(key);
      
      // 2. 删除原密钥
      await this.deleteKey(key);

      // 3. 使用相同的配置生成新密钥
      const generateOptions = {
        user_id: keyInfo.user_id,
        models: keyInfo.models || ['deepseek-chat', 'qwen3-235b-a22b'],
        key_alias: keyInfo.key_alias || keyInfo.user_id,
        max_budget: keyInfo.max_budget,
        metadata: {
          ...keyInfo.metadata,
          regenerated_from: key,
          regenerated_at: new Date().toISOString()
        },
        ...options // 允许覆盖配置
      };

      const newKey = await this.generateKey(generateOptions);
      
      return {
        key: newKey.key,
        message: '密钥重新生成成功',
        old_key: key,
        new_key: newKey.key
      };
    } catch (error) {
      console.error('重新生成密钥错误:', error);
      throw error;
    }
  }

  // 获取用户的所有密钥（带缓存）
  async getUserKeys(userId, userInfo = null) {
    try {
      // 清理过期缓存
      this.cleanExpiredCache();
      
      // 检查缓存
      const cached = this.getCachedData(this.cache.userKeys, userId, this.cacheTimeout.userKeys);
      if (cached) {
        console.log(`[缓存] 使用缓存的用户密钥: ${userId}`);
        return cached;
      }

      console.log(`[API] 获取用户密钥: ${userId}`);
      
      // 如果已经有用户信息，直接使用，避免重复请求
      const info = userInfo || await this.getUserInfo(userId);
      
      let processedKeys = [];
      
      // 如果用户信息中包含密钥列表，返回它们
      if (info.keys && Array.isArray(info.keys)) {
        
        // 处理密钥列表，获取完整的Secret Key
        processedKeys = info.keys.map((keyData) => {
          try {
            // 从localStorage获取完整的Secret Key（使用user_id作为key）
            const storedKey = localStorage.getItem(`litellm_key_${userId}`);
            
            // 确保删除操作使用正确的标识符
            // LiteLLM删除API需要的是token字段，而不是完整的sk-开头密钥
            const result = {
              ...keyData,
              // 保留原始的token字段用于删除操作
              token: keyData.token,
              // 保留其他标识字段
              key_name: keyData.key_name,
              // 如果有完整密钥，用于显示；否则显示截断版本
              key: storedKey && storedKey.startsWith('sk-') ? storedKey : (keyData.key_name || keyData.token),
              // 标记是否为部分密钥
              isPartialKey: !(storedKey && storedKey.startsWith('sk-'))
            };
            
            console.log(`[处理密钥] 用户 ${userId} - token: ${keyData.token ? keyData.token.substring(0, 10) + '...' : '未知'}, 完整密钥: ${storedKey ? '已存储' : '未存储'}`);
            
            return result;
          } catch (error) {
            console.error('处理密钥数据错误:', error);
            return {
              ...keyData,
              token: keyData.token, // 保留token用于删除
              key: keyData.key_name || keyData.token,
              isPartialKey: true
            };
          }
        });
      }

      // 缓存结果
      this.setCachedData(this.cache.userKeys, userId, processedKeys);
      
      return processedKeys;
    } catch (error) {
      console.error('获取用户密钥错误:', error);
      throw error;
    }
  }

  // 测试连接（带缓存）
  async testConnection() {
    try {
      const now = Date.now();
      
      // 检查缓存
      if (this.cache.connection !== null && 
          (now - this.cache.lastConnectionCheck) < this.cacheTimeout.connection) {
        console.log('[缓存] 使用缓存的连接状态');
        return this.cache.connection;
      }

      console.log('[API] 测试连接状态');
      const response = await fetch(`${LITELLM_API_BASE}/health`, {
        method: 'GET',
        headers: this.getHeaders()
      });

      const isConnected = response.ok;
      
      // 缓存结果
      this.cache.connection = isConnected;
      this.cache.lastConnectionCheck = now;
      
      return isConnected;
    } catch (error) {
      console.error('测试连接错误:', error);
      
      // 缓存失败结果（较短时间）
      this.cache.connection = false;
      this.cache.lastConnectionCheck = Date.now();
      
      return false;
    }
  }

  // 验证主密钥（带缓存）
  async validateMasterKey() {
    try {
      const now = Date.now();
      
      // 检查缓存
      if (this.cache.masterKeyValid !== null && 
          (now - this.cache.lastMasterKeyCheck) < this.cacheTimeout.masterKey) {
        console.log('[缓存] 使用缓存的主密钥验证结果');
        return this.cache.masterKeyValid;
      }

      console.log('[API] 验证主密钥');
      
      // 使用健康检查端点验证主密钥，避免不必要的测试请求
      const healthResponse = await fetch(`${LITELLM_API_BASE}/health`, {
        method: 'GET',
        headers: this.getHeaders()
      });

      // 健康检查成功表示主密钥有效
      const isValid = healthResponse.ok;
      
      // 缓存结果
      this.cache.masterKeyValid = isValid;
      this.cache.lastMasterKeyCheck = now;
      
      return isValid;
    } catch (error) {
      console.error('验证主密钥错误:', error);
      
      // 缓存失败结果（较短时间）
      this.cache.masterKeyValid = false;
      this.cache.lastMasterKeyCheck = Date.now();
      
      return false;
    }
  }

  // 清理过期缓存
  cleanExpiredCache() {
    const now = Date.now();
    
    // 清理用户信息缓存
    for (const [key, data] of this.cache.userInfo.entries()) {
      if (now - data.timestamp > this.cacheTimeout.userInfo) {
        this.cache.userInfo.delete(key);
      }
    }
    
    // 清理用户密钥缓存
    for (const [key, data] of this.cache.userKeys.entries()) {
      if (now - data.timestamp > this.cacheTimeout.userKeys) {
        this.cache.userKeys.delete(key);
      }
    }
  }

  // 获取缓存的数据
  getCachedData(cacheMap, key, timeout) {
    const cached = cacheMap.get(key);
    if (cached && (Date.now() - cached.timestamp) < timeout) {
      return cached.data;
    }
    return null;
  }

  // 设置缓存数据
  setCachedData(cacheMap, key, data) {
    cacheMap.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  // 清理特定用户的缓存
  clearUserCache(userId) {
    this.cache.userInfo.delete(userId);
    this.cache.userKeys.delete(userId);
    console.log(`[缓存] 清理用户 ${userId} 的缓存`);
  }

  // 清理所有缓存
  clearAllCache() {
    this.cache.userInfo.clear();
    this.cache.userKeys.clear();
    this.cache.connection = null;
    this.cache.masterKeyValid = null;
    this.cache.lastConnectionCheck = 0;
    this.cache.lastMasterKeyCheck = 0;
    console.log('[缓存] 清理所有缓存');
  }
}

// 创建单例实例
const apiKeyService = new APIKeyService();

export default apiKeyService; 